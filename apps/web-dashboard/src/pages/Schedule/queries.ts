import { gql } from '@apollo/client'

export const GET_LIVE_FROM_SCHEDULE_ENABLED = gql`
  query GetLiveFromScheduleEnabled {
    me {
      uiPermissions {
        dashboardLiveFromScheduleEnabled
      }
    }
  }
`

export const GET_BLOCK_TIME_DATA = gql`
  query GetBlockTimeData(
    $roomIds: [ID!]
    $minEndTime: DateTime!
    $maxStartTime: DateTime!
  ) {
    blockTimes(
      query: {
        roomIds: $roomIds
        minEndTime: $minEndTime
        maxStartTime: $maxStartTime
      }
    ) {
      edges {
        node {
          id
          blockId
          startTime
          endTime
          roomId
          releasedFrom
          createdTime
          room {
            id
            name
          }
          block {
            id
            name
            color
            surgeonIds
            archivedTime
          }
          releases(includeUnreleased: true) {
            id
            blockTimeId
            reason
            startTime
            endTime
            source
            sourceType
            releasedTime
            unreleasedTime
            unreleasedSource
          }
        }
      }
    }
  }
`

export const GET_TIMELINE_CASE_DATA = gql`
  query GetTimelineCaseData(
    $siteIds: [String!]
    $roomIds: [String!]
    $minEndTime: DateTime!
    $maxStartTime: DateTime!
    $caseMatchingStatuses: [CaseMatchingStatus!]
    $includeStaffPlan: Boolean!
    $includeNotePlan: Boolean!
    $includeTurnovers: Boolean!
    $includePatientData: Boolean!
    $includeEventNotifications: Boolean!
    $statusFilter: [RoomStatusName!]
  ) {
    sites(siteIds: $siteIds) {
      edges {
        node {
          id
          name
          turnoverGoals {
            goalMinutes
            maxMinutes
          }
          rooms(roomIds: $roomIds, statusFilter: $statusFilter) {
            edges {
              node {
                sortKey
                status {
                  name
                  since
                  inProgressTurnover {
                    id
                    startTime
                    endTime
                    type
                  }
                  inProgressApellaCase {
                    id
                    startTime
                    endTime
                    status(useObservations: true) {
                      name
                      since
                    }
                  }
                }
                id
                name
                primeTimeConfig {
                  id
                  sunday {
                    startTime
                    endTime
                  }
                  monday {
                    startTime
                    endTime
                  }
                  tuesday {
                    startTime
                    endTime
                  }
                  wednesday {
                    startTime
                    endTime
                  }
                  thursday {
                    startTime
                    endTime
                  }
                  friday {
                    startTime
                    endTime
                  }
                  saturday {
                    startTime
                    endTime
                  }
                }
                turnovers(
                  query: {
                    minEndTime: $minEndTime
                    maxStartTime: $maxStartTime
                  }
                ) @include(if: $includeTurnovers) {
                  id
                  startTime
                  endTime
                  type
                  followingCase {
                    id
                    case {
                      scheduledStartTime
                    }
                  }
                  precedingCase {
                    id
                  }
                  labels {
                    id
                  }
                  note
                }
                apellaCases(
                  query: {
                    minEndTime: $minEndTime
                    maxStartTime: $maxStartTime
                    caseMatchingStatuses: $caseMatchingStatuses
                  }
                ) {
                  edges {
                    node {
                      id
                      type
                      startTime
                      endTime
                      status(useObservations: true) {
                        name
                        since
                      }
                      actual {
                        id
                        room {
                          id
                          name
                        }
                        startTime
                        endTime
                      }
                      case {
                        id
                        scheduledStartTime
                        scheduledEndTime
                        isFirstCase
                        isInFlipRoom
                        isAddOn
                        patientClass
                        eventNotifications
                          @include(if: $includeEventNotifications) {
                          sentTime
                          event {
                            id
                            name
                            color
                            attrs {
                              name
                            }
                          }
                          observation {
                            id
                            observationType {
                              id
                              name
                              color
                            }
                          }
                          staffEventContactInformation {
                            contactInformation {
                              firstName
                              lastName
                              isApellaEmployee
                            }
                          }
                        }
                        serviceLine {
                          id
                          name
                        }
                        externalCaseId
                        precedingCase {
                          id
                        }
                        caseMatchingStatus
                        caseClassificationType {
                          id
                          name
                        }
                        caseFlags(includeArchived: false)
                          @include(if: $includeStaffPlan) {
                          id
                          flagType
                          archivedTime
                        }
                        caseLabels @include(if: $includeNotePlan) {
                          abbreviation
                          color
                          fieldId
                          id
                          optionId
                          value
                        }
                        caseStaff {
                          role
                          staff {
                            id
                            firstName
                            lastName
                          }
                        }
                        patient @include(if: $includePatientData) {
                          id
                          personalInfo {
                            firstNameAbbreviated
                            lastNameAbbreviated
                            age
                            administrativeSex {
                              text
                            }
                          }
                        }
                        caseStaffPlan(query: { includeArchived: false })
                          @include(if: $includeStaffPlan) {
                          edges {
                            node {
                              id
                              role
                              staff {
                                id
                                firstName
                                lastName
                              }
                            }
                          }
                        }
                        notePlan @include(if: $includeNotePlan) {
                          id
                          note
                        }
                        primaryCaseProcedures {
                          procedure {
                            id
                            name
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`

export const PUBLISH_CASE_MATCHING_EDITS = gql`
  mutation PublishCaseMatchingEdits($input: [MatchCaseInput!]!) {
    matchCases(input: $input) {
      success
    }
  }
`
export const GET_TURNOVER_LABELS = gql`
  query GetTurnoverLabels($query: GQLTurnoverLabelQueryInput!) {
    turnoverLabels(query: $query) {
      id
      name
      type
    }
  }
`

export const GET_BLOCK_UTILIZATIONS_FOR_SITE = gql`
  query GetBlockUtilizationForSite($query: BlockUtilizationInput!) {
    blockUtilizations(query: $query) {
      blockId
      utilizedSeconds
      availableSeconds
      utilizedScheduledSeconds
      casesForBlockDay {
        caseId
      }
    }
  }
`
